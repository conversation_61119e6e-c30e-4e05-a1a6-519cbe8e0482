{"name": "single-web", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:stage": "vite --mode stage", "dev:mock": "vite --mode mock", "dev:remove:.vite": "rm -rf node_modules/.vite && vite", "build-test": "tsc && vite build --mode development", "build": "vite build", "build:test": "vite build  --mode test", "build:stage": "vite build  --mode stage", "build:prod": "vite build  --mode production", "lint": "eslint ./src --fix --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "prepare": "husky install", "sync-flag": "ts-node --project tsconfig.script.json  ./scripts/sync-flag.ts"}, "lint-staged": {"src/**/*.{ts,tsx}": ["prettier --write", "eslint --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.48", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@ice/stark-app": "^1.5.0", "@ice/stark-data": "^0.1.3", "@sentry/react": "^10.5.0", "@sentry/tracing": "7.64.0", "@tanstack/react-query": "^5.17.19", "@tastien/thooks": "^1.0.2", "@types/react-window": "^1.8.8", "ahooks": "^3.7.10", "ali-oss": "^6.20.0", "antd": "^5.15.0", "axios": "^1.6.7", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "install": "^0.13.0", "loadsh": "^0.0.4", "lodash": "^4.17.21", "memoize-one": "^6.0.0", "mobx": "^6.12.0", "mobx-react": "^9.1.0", "moment": "^2.30.1", "npm": "^10.5.0", "numeral": "^2.0.6", "qs": "^6.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "react-router-dom": "^6.21.3", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "tailwind-merge": "^2.5.4", "uuid": "^9.0.1", "vite-plugin-imp": "^2.4.0"}, "devDependencies": {"@sentry/vite-plugin": "^4.1.1", "@tastien/eslint-config-tastien": "0.0.10", "@testing-library/jest-dom": "^6.3.0", "@testing-library/react": "^14.1.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.0", "@types/mockjs": "^1", "@types/numeral": "^2.0.5", "@types/qs": "^6.9.12", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^4.0.1", "hash.js": "^1.1.7", "husky": "^9.0.7", "jest": "^29.7.0", "less": "^4.2.0", "lint-staged": "^15.2.1", "mockjs": "^1.1.0", "postcss": "^8.4.33", "prettier": "^3.2.5", "sass": "^1.71.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "ts-jest-mock-import-meta": "^1.1.0", "ts-node": "^10.9.2", "typescript": "5.2.2", "vite": "^5.0.8", "vite-plugin-index-html": "^2.0.2", "vite-plugin-mock": "^3.0.1"}, "packageManager": "yarn@1.22.22"}