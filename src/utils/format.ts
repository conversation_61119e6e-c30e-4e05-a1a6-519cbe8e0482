import dayjs from 'dayjs';
import { DateQueryType } from '@/components/date-query-select';
import { StoreStatusCN } from '@/constants/organization';

export const formatSopCategoriesToOptions = (data: any) => {
  return data?.map(({ id, categoryName }: any) => ({
    value: id,
    label: categoryName,
  }));
};

export const formatShopTypeOptions = (data: any) => {
  return data?.map(({ code, desc }) => ({
    value: code,
    label: desc,
  }));
};

export const formatToOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ id, name, value, taskType }: any) => ({ value: id, label: name || value, type: taskType })),
  );
};

export const formatAuditLearnToOptions = (data: { projectName: string; projectId: string }[]) => {
  return Promise.resolve(data?.map(({ projectName, projectId }) => ({ value: projectId, label: projectName })));
};

// 转换中台接口用户数据成 创建人、巡检人、点评人等筛选数据
export const formatUserListToRoleOptions = (data: any, showStaffCode = false) => {
  return data?.map(({ userId, nickName, staffCode }: any, index: number) => ({
    value: userId || index,
    label: showStaffCode ? `${nickName || ''}${staffCode ? `（${staffCode}）` : ''}` : nickName,
  }));
};

// 转换中台接口用户数据成 创建人、巡检人、点评人等筛选数据
export const formatUserListToOptions = (data: any) => {
  return data?.map(({ userId, nickname, mobile }: any, index: number) => ({
    value: userId || index,
    label: `${nickname} (${mobile})`,
  }));
};

export const formatOrganizationsToOptions = (data: any) => {
  const convert = ({ name, id, children }: any) => {
    return { value: id, label: name, children: children?.map((item) => convert(item)) };
  };

  return Promise.resolve([convert(data)]);
};

export const formatOrganizationTreeToOptions = (data: any) => {
  const convert = ({ name, id, children }: any) => {
    return { value: id, label: name, children: children?.map((item) => convert(item)) };
  };

  return Promise.resolve((data || []).map((item) => convert(item)));
};

export const formatOMOrganizationTreeToOptions = (data: any) => {
  const convert = ({ name, id, treeSet }: any) => {
    return { value: id, label: name, children: treeSet?.map((item) => convert(item)) };
  };

  return Promise.resolve((data?.treeSet || []).map((item) => convert(item)));
};

export const formatChecklistCategoriesToOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ id, categoryName, sort, itemCount }: any) => ({ value: id, label: categoryName, sort, itemCount })),
  );
};

export const formatChecklistToOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ id, sheetName, type, totalScore }: any) => ({ value: id, label: sheetName, type, totalScore })),
  );
};

export const formatShopsToOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ shopId, shopName, shopStatus }: any) => ({
      value: shopId,
      label: `${shopName}  (${shopId})  ${shopStatus ? `(${StoreStatusCN?.[shopStatus]})` : ''}`,
    })),
  );
};

export const formatOrganizationAndShopTreeToOptions = (data) => {
  // let orgMap = {};
  const convert = ({ id, name, type, shopId, children, shopType, shopStatus }) => {
    const value = type === 1 ? `ORGANIZATION-${id}` : `SHOP-${shopId}`;
    const label = type === 1 ? name : `${shopId} ${name}`;

    // orgMap[value] = label;
    return {
      value,
      label,
      title: label,
      key: value,
      type,
      shopType,
      shopStatus,
      children: children?.map((item) => convert(item)),
    };
  };

  const options = data?.map((item) => convert(item));

  return Promise.resolve({ options });
};

export const formatRoleToOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ roleClassifyName, roleClassifyId, roleBaseInfos }) => ({
      value: roleClassifyId,
      label: roleClassifyName,
      children: roleBaseInfos?.map(({ roleId, roleName }) => ({ value: roleId, label: roleName })),
    })),
  );
};

export const formatVideoTime = (videoTime) => {
  if (!videoTime) return '-';

  const hour = Math.floor(videoTime / 60 / 60);
  const minute = Math.floor((videoTime - hour * 60 * 60) / 60);
  const second = videoTime - hour * 60 * 60 - minute * 60;

  return `${hour < 10 ? `0${hour}` : hour}:${minute < 10 ? `0${minute}` : minute}:${
    second < 10 ? `0${second}` : second
  }`;
};

export const formatClockTime = (videoTime) => {
  if (!videoTime) return '-';

  const hour = Math.floor(videoTime / 60 / 60 / 1000);
  const minute = Math.floor((videoTime - hour * 60 * 60) / 60 / 1000);
  const second = videoTime / 1000 - hour * 60 * 60 - minute * 60;

  return `${hour < 10 ? `0${hour}` : hour}:${minute < 10 ? `0${minute}` : minute}:${
    second < 10 ? `0${second}` : second
  }`;
};

export const formatCitiesToOptions = (data) => {
  return Promise.resolve(
    data.map(({ province, city }) => {
      return {
        label: `${province} ${city}`,
        value: `${province} ${city}`,
      };
    }),
  );
};

export const formatTemplatetoOptions = (data: any) => {
  console.log(data);

  return Promise.resolve(
    data?.map(({ id, templateName }) => ({
      value: id,
      label: templateName,
    })),
  );
};

export const formatStrategyNameOptions = (data: any) => {
  return Promise.resolve(
    data?.map(({ id, strategyName }) => ({
      value: id,
      label: strategyName,
    })),
  );
};

export const formatStrategyNamePageOptions = (data: any) => {
  return Promise.resolve(
    data?.data?.map(({ id, strategyName }) => ({
      value: id,
      label: strategyName,
    })),
  );
};

export const formatAssociatedCheckItemToOptions = (data: any) => {
  const loop = (nodes) => {
    return nodes?.map(({ typeId, name, data }) => {
      return {
        label: name,
        value: typeId,
        children: data?.map(({ id, name }) => ({ value: id, label: name })),
      };
    });
  };

  return Promise.resolve(loop(data));
};

export const formatQueryDate = (dateQuery: any) => {
  const format: string = 'YYYY-MM-DD HH:mm:ss';

  let startDate: any;
  let endDate: any;

  // if (dateQuery?.type === DateQueryType.ReportSubmissionTime) {
  if ([DateQueryType.ReportSubmissionTime, DateQueryType.BeginpatrolDate].includes(dateQuery?.type)) {
    startDate = dayjs(dateQuery?.date?.[0]).format(format);
    endDate = dayjs(dateQuery?.date?.[1]).format(format);
  } else {
    if (Array.isArray(dateQuery?.date)) {
      startDate = dayjs(dateQuery?.date?.[0])?.startOf('day').format(format);
      endDate = dayjs(dateQuery?.date?.[1])?.endOf('day').format(format);
    } else {
      startDate = dayjs(dateQuery?.date)?.startOf('day').format(format);
      endDate = dayjs(dateQuery?.date)?.endOf('day').format(format);
    }
  }

  return { startDate, endDate };
};

// 区县树
type AreaTreeNode = TreeWith<{
  key: number;
  id: number;
  parentId: number;
  name: string;
  label: string;
  value: string;
  suffix: string;
}>;

export const convertListToTree = (list: { id: number; parentId: number; name: string }[]) => {
  const menuObj: { [key: number]: AreaTreeNode } = {};

  const tree: AreaTreeNode[] = [...list] as AreaTreeNode[];

  tree.forEach((item) => {
    menuObj[item.id] = item;
  });

  return tree.filter((item) => {
    const { id, parentId, name } = item;

    // menuObj[id].key = id;
    menuObj[id].label = name;
    menuObj[id].value = name;

    if (parentId !== 0) {
      const { children } = menuObj[item.parentId] || {};

      if (menuObj[item.parentId]) {
        menuObj[item.parentId].children = [...(children || []), item];
      }

      return false;
    }

    return true;
  });
};
