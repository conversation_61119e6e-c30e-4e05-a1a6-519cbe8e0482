import { useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import Service from './service';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';
import { PaginationParams } from '@/utils/pagination';
import { renderPage } from '@/utils/render';

export enum DetailType {
  Add = 'Add', // 新增
  Readonly = 'Readonly', // 只读
  Edit = 'Edit', // 编辑
  Copy = 'Copy', // 复制
}

const WarningMessageIndex = renderPage(() => {
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [service, executeRequest] = Service();
  const navigate = useGlobalNavigate();

  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _wMessage = parserParams(urlParams?.wMessage || {});
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });

  const columns = getColumns({
    searchColumns: [],
    tableColumns: [],
  });

  useEffect(() => {}, []);

  return (
    <ProTable
      rowKey="id"
      {...getCommonConfig({
        search: {
          form,
        },
        manualRequest: true,
      })}
      actionRef={actionRef}
      options={false}
      columns={columns}
      request={async (values) => {
        console.log('💀 ~ async ~ values:', values);

        return {
          data: [],
          success: true,
          total: 0,
        };
      }}
      pagination={{
        showSizeChanger: true,
        current: paginationParams.pageNo,
        pageSize: paginationParams.pageSize,
      }}
      onChange={({ current, pageSize }) => {
        setPaginationParams((p) => ({
          ...p,
          pageNo: current ?? 1,
          pageSize: pageSize ?? 10,
        }));
      }}
      tableExtraRender={() => {
        return (
          <ProCard>
            <Button
              type="primary"
              onClick={() => {
                navigate(RouteKey.WMessageDetail, {
                  searchParams: { operateType: DetailType.Add },
                });
              }}
            >
              创建预警消息模板
            </Button>
          </ProCard>
        );
      }}
      tableRender={(_p, _d, { table }) => {
        return <ProCard>{table}</ProCard>;
      }}
    />
  );
});

export default WarningMessageIndex;
