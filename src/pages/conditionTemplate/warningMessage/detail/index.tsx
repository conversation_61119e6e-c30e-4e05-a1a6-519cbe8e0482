import { ProCard, ProForm, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useGlobalNavigate from '@/router/useGlobalNavigate';

export default function DetailIndex() {
  const [form] = ProForm.useForm();
  const [params] = useQuerySearchParams();
  const navigate = useGlobalNavigate();

  return (
    <ProCard>
      <ProForm form={form} layout="horizontal" labelCol={{ span: 2 }}>
        <ProFormText
          name="name"
          label="消息模版名称"
          width="md"
          rules={[{ required: true, message: '请输入消息模版名称' }]}
        />
        <ProFormSelect
          name="taskType"
          label="关联任务类型"
          width="md"
          rules={[{ required: true, message: '请选择关联任务类型' }]}
        />
        <ProFormText
          name="messageTitle"
          label="消息名称"
          width="md"
          rules={[{ required: true, message: '请输入消息名称' }]}
        />
        <div className="flex">
          <ProFormTextArea
            width="md"
            label="消息内容"
            name="messageContent"
            placeholder="请输入消息内容"
            rules={[
              {
                required: true,
                message: '请输入消息内容',
              },
            ]}
            fieldProps={{
              maxLength: 400,
              showCount: true,
              autoSize: {
                minRows: 6,
              },
            }}
          />
        </div>
      </ProForm>
    </ProCard>
  );
}
