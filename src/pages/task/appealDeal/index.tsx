import { useEffect } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { ProForm, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import { getSearchColumns, getTableColumns } from './columns';
import { useDebounceDownload } from './hooks';
import Service, { RequestName } from './service';
import { formatParams, toArrayJson } from './utils';
import { DescModal } from '../complaints/components/DescModal';
import DescTacticsModal from '../complaints/components/DescTacticsModal';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';

type TProps = {
  /** 是否为策略 */
  isTactics?: boolean;
};

export default function AppealDeal({ isTactics }: TProps) {
  const [service, executeRequest] = Service();
  const [form] = ProForm.useForm();
  const [searchParams, setSearchParams] = useQuerySearchParams();

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (Object.keys(searchParams || {})?.length) {
      form.setFieldsValue(searchParams);
    }
  }, [form, searchParams]);

  const { run: downloadDetailRun, loading: downloadLoading } = useDebounceDownload({ searchParams, isTactics });

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const columns = getColumns({
    searchColumns: getSearchColumns({
      service,
      executeRequest,
      RequestName,
      isTactics,
    }),
    tableColumns: getTableColumns({
      onView: (record) => {
        const modal = isTactics ? DescTacticsModal : DescModal;

        modal.showModal({
          data: {
            init: record,
          },
        });
      },
    }),
  });

  return (
    <>
      <ProTable
        className="table-hidden-sticky"
        tableClassName="pt-4"
        {...commonConfig}
        options={false}
        columns={columns}
        scroll={{
          x: 1200,
        }}
        sticky={{
          offsetHeader: 56,
        }}
        request={async (params) => {
          const values = formatParams({ ...form.getFieldsValue(), ...params });

          const res = await executeRequest(
            isTactics ? RequestName.AppealTacticsDealList : RequestName.AppealDealList,
            values,
          );

          setSearchParams({
            ...searchParams,
            ...values,
            auditTime: params?.auditTime,
            statuses: values?.statuses ? toArrayJson(values.statuses) : undefined,
          });

          return {
            data: res?.data ?? [],
            success: true,
            total: res?.total ?? 0,
          };
        }}
        tableExtraRender={({ pagination }) => (
          <div className="bg-white p-4 rounded-md flex justify-end">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              loading={downloadLoading}
              disabled={(typeof pagination === 'object' && !pagination?.total) || downloadLoading}
              onClick={downloadDetailRun}
            >
              下载明细
            </Button>
          </div>
        )}
        form={{
          ignoreRules: false,
        }}
      />
      {isTactics ? <DescTacticsModal /> : <DescModal />}
    </>
  );
}
