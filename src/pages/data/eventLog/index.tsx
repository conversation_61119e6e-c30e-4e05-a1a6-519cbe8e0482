import { useEffect, useState } from 'react';
import { ProCard, ProForm, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import { eventTypeOption, eventTypeValueMap } from './contanst';
import Service from './service';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';
import { PaginationParams } from '@/utils/pagination';

export default function EventLogIndex() {
  const [form] = ProForm.useForm();
  const [service, executeRequest] = Service();
  const navigate = useGlobalNavigate();

  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _eventLog = parserParams(urlParams?.eventLog || {});
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [eventType, setEventType] = useState<string>();

  const columns = getColumns({
    searchColumns: [
      {
        title: '事件类型',
        dataIndex: 'eventType',
        valueType: 'select',
        fieldProps: {
          options: eventTypeOption,
          onChange: (value: string) => {
            // 当事件类型改变时，清空事件值并更新状态
            form.setFieldValue('eventValue', undefined);
            setEventType(value);
          },
        },
      },
      {
        dataIndex: 'eventValue',
        hideInTable: true,
        renderFormItem: () => {
          // 当eventType为undefined时，不展示这个select
          if (!eventType) {
            return null;
          }

          // 根据事件类型决定标题和选项
          let title = '事件值';
          let options = [];

          if (eventType === 'PROGRESS_OF_WORKS') {
            title = '开业进度';
            options = eventTypeValueMap.PROGRESS_OF_WORKS || [];
          } else if (eventType === 'RESIGNATION_HANDOVER') {
            title = '交接方';
            options = eventTypeValueMap.RESIGNATION_HANDOVER || [];
          }

          return (
            <ProFormSelect
              name="eventValue"
              label={title}
              options={options}
              placeholder={`请选择${title}`}
              allowClear
            />
          );
        },
      },
    ],
    tableColumns: [],
  });

  useEffect(() => {
    // 监听表单值变化，同步eventType状态
    const currentEventType = form.getFieldValue('eventType');

    if (currentEventType !== eventType) {
      setEventType(currentEventType);
    }
  }, [form, eventType]);

  return (
    <ProTable
      rowKey="id"
      {...getCommonConfig({
        search: {
          form,
        },
        manualRequest: true,
      })}
      options={false}
      columns={columns}
      request={async (values) => {
        console.log('💀 ~ async ~ values:', values);

        return {
          data: [],
          success: true,
          total: 0,
        };
      }}
      onChange={({ current, pageSize }) => {
        setPaginationParams((p) => ({
          ...p,
          pageNo: current ?? 1,
          pageSize: pageSize ?? 10,
        }));
      }}
      tableRender={(props: any, _d, { table }) => {
        return (
          <ProCard
            extra={
              <Button
                type="primary"
                onClick={() => {
                  // executeRequest(RequestName.ExportReportedItemList, formatParams(form?.getFieldsValue())).then(() => {
                  //   layoutStore.increaseExport();
                  // });
                }}
              >
                下载明细
              </Button>
            }
          >
            {table}
            <PreviousPageAndNextPageButton
              pageNum={paginationParams.pageNo}
              pageSize={paginationParams.pageSize}
              dataLength={props?.action?.dataSource?.length ?? 0}
              loading={props?.action?.loading}
              onChange={({ pageNum, pageSize }) => {
                setPaginationParams((p) => ({
                  ...p,
                  pageNo: pageNum,
                  pageSize,
                }));
              }}
            />
          </ProCard>
        );
      }}
      pagination={false}
      params={paginationParams}
      onSubmit={() => {
        setPaginationParams((p) => ({
          ...p,
          pageNo: 1,
        }));
      }}
      onReset={() => {
        setPaginationParams((p) => ({
          ...p,
          pageNo: 1,
        }));
      }}
    />
  );
}
