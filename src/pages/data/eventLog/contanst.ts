const eventTypeOption = [
  {
    label: '开业进度节点',
    value: 'PROGRESS_OF_WORKS',
  },
  {
    label: '中台-人员交接事件',
    value: 'RESIGNATION_HANDOVER',
  },
];

const eventTypeValueMap = {
  PROGRESS_OF_WORKS: [
    {
      label: '开工令下发',
      value: 'START_WORK_ORDER_ISSUED',
    },
    {
      label: '隐蔽验收开始',
      value: 'HIDDEN_ACCEPTANCE_STARTED',
    },
    {
      label: '提交竣工验收报告',
      value: 'SUBMIT_COMPLETION_ACCEPTANCE_REPORT',
    },
    {
      label: '提交食安许可证时间',
      value: 'SUBMIT_BUSINESS_LICENSE_TIME',
    },
    {
      label: '预计开业时间-3天',
      value: 'EXPECTED_OPENING_TIME_MINUS_THREE_DAYS',
    },
  ],
  RESIGNATION_HANDOVER: [
    {
      label: '中台-人员交接事件-移交方',
      value: 'HANDOVER_PARTY',
    },
    {
      label: '中台-人员交接事件-承接方',
      value: 'RECEIVING_PARTY',
    },
  ],
};

export { eventTypeOption, eventTypeValueMap };
