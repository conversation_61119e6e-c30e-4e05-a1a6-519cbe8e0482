import { useEffect, useMemo, useState } from 'react';
import { ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { Tooltip } from 'antd';
import { debounce } from 'lodash';

export interface DebounceSelectProps<ValueType = any>
  extends Omit<ProFormSelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
  requestOptions: (search: string, pageNo: number) => Promise<ValueType[]>;
  debounceTimeout?: number;
  name?: string;
  label?: string;
  existeds?: any[];
}

function DebounceSelect<ValueType extends { key?: string; label: string; value: string | number } = any>({
  requestOptions,
  debounceTimeout = 800,
  label,
  existeds,
  ...props
}: DebounceSelectProps<ValueType>) {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<ValueType[]>([]);
  const [searchValue, setSearchValue] = useState<string>();
  const [pageNo, setPageNo] = useState(1); // 页码
  const [pages, setPages] = useState(0); // 总页数

  useEffect(() => {
    loadOptions(searchValue, 1);
  }, []);

  const debounceSearch = useMemo(() => {
    const inputSearch = (value) => {
      setSearchValue(value);
      setPageNo(() => {
        loadOptions(value, 1);

        return 1;
      });
    };

    return debounce(inputSearch, debounceTimeout);
  }, [debounceTimeout]);

  const loadOptions = (value, pageNo) => {
    setLoading(true);
    requestOptions(value, pageNo).then((res: any) => {
      let newOptions = [...options];

      if (pageNo === 1) {
        newOptions = res?.result;
      } else {
        newOptions = newOptions.concat(res?.result || []);
      }

      setOptions(newOptions || []);
      setPages(res?.pages || 0);
      setLoading(false);
    });
  };

  const filterOptions = useMemo(() => {
    const optionIds = options?.map(({ userId }: any) => userId);

    const otherOptions = existeds
      ?.filter(({ userId }) => !optionIds.includes(userId))
      .map(({ userId, nickname }) => ({ userId, name: nickname }));

    return options?.concat((otherOptions as any) || []) || [];
  }, [existeds, options]);

  return (
    <ProFormSelect
      label={label}
      fieldProps={{
        labelInValue: true,
        filterOption: false,
        searchValue,
        onSearch: (value) => {
          debounceSearch(value);
        },
        showSearch: true,
        loading,
        autoClearSearchValue: false,
        mode: 'multiple',
        fieldNames: {
          value: 'userId',
          label: 'name',
        },
        onPopupScroll: (e: any) => {
          const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

          if (scrollTop + clientHeight >= scrollHeight - 10 && !loading) {
            if (pageNo < pages) {
              setPageNo(() => {
                const newPageNo = pageNo + 1;

                loadOptions(searchValue, newPageNo);

                return newPageNo;
              });
            }
          }
        },
        optionRender: (option) => {
          const { data } = option;
          const { name, groupItems, shopItems } = data;
          const groupNames = groupItems?.map(({ name }) => name);
          const shopNames = shopItems?.map(({ shopName }) => shopName);
          const orgText = groupNames?.concat(shopNames)?.join('、');

          return (
            <div className="w-full flex justify-between">
              <div>{name}</div>
              <Tooltip placement="top" title={orgText}>
                <div className="max-w-[50%] text-ellipsis whitespace-nowrap overflow-hidden">{orgText}</div>
              </Tooltip>
            </div>
          );
        },
      }}
      {...props}
      options={filterOptions}
    />
  );
}

export default DebounceSelect;
