import path, { resolve } from 'path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import htmlPlugin from 'vite-plugin-index-html';
import { viteMockServe } from 'vite-plugin-mock';

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), '');

  const plugins = [
    react(),
    htmlPlugin({
      input: resolve(__dirname, './src/main.tsx'),
      preserveEntrySignatures: 'exports-only',
    }),

    viteMockServe({
      mockPath: 'mock',
      enable: mode === 'mock',
    }),
    // VitePluginImp({
    //   libList: [{ libName: 'antd', style: (name) => `antd/es/${name}/style` }]
    // })
  ];

  if (mode === 'production') {
    plugins.push(
      sentryVitePlugin({
        org: 'tastien',
        project: 'om-manage',
        url: 'https://sentry2.tastientech.com/',
        authToken: env.SENTRY_AUTH_TOKEN,
      }),
    );
  }

  return {
    server: {
      // 指定dev sever的端口号，默认为5173
      port: 3000,
      // 自动打开浏览器运行以下页面
      open: '/',
      cors: true,
      proxy: {
        '/cc-api': {
          target: env.CC_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/cc-api/, '/api'),
        },
        '/om-api': {
          target: env.OM_URL,
          // target: 'http://10.8.203.94:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
        '/rm-api': {
          target: env.OM_URL,
          // target: 'http://10.8.203.94:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
        '/tm-api': {
          target: env.OM_URL,
          // target: 'http://10.9.204.183:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    build: {
      sourcemap: true,
    },
    plugins,
    base: env.VITE_BASE_URL,
  };
});
