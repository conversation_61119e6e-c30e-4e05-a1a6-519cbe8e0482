# .env.production
# 这个文件是配置sentry的环境变量的模版，将其中的变量值替换为自己的值

# 关于sentry的配置，可以参考：https://docs.sentry.io/platforms/node/sourcemaps/uploading/vite/
SENTRY_ORG="sentry"
SENTRY_PROJECT="sentry"
SENTRY_URL="https://sentry.io/"
SENTRY_AUTH_TOKEN="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"

# VITE这个前缀是vite框架的环境变量前缀，如果不加VITE前缀那么在运行时环境将取不到这个变量，具体见下面这个文档
# @see https://cn.vitejs.dev/guide/env-and-mode.html
VITE_SENTRY_DSN="https://<EMAIL>/27"

# 关于scripts脚本的配置
API_URL='https://erp.tastientech.com/'

CC_URL='https://tastien.tastientech.com/'

OM_URL="https://gzt.tastientech.com/"

VITE_BASE_URL="https://gzt.tastientech.com/"

VITE_BASE="//gzt.tastientech.com"

VITE_BRAND_ID="105717"

VITE_MODE="production"

VITE_BURYING_POINT="https://mdlog.tastientech.com"

VITE_GAIA_API_URL="https://tastien.tastientech.com"

VITE_TOKEN_KEY="tst-user-token"